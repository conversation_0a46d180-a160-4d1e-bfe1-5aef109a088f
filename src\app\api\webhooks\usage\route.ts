import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { subscription, subscriptionUsage } from '@/db/schema';
import { eq, and, gte, lte } from 'drizzle-orm';

// POST - Handle usage webhook events
export async function POST(request: NextRequest) {
    try {
        // Verify webhook signature (implement based on your usage tracking system)
        // const signature = request.headers.get('x-webhook-signature');
        // if (!verifyWebhookSignature(request.body, signature)) {
        //   return NextResponse.json(
        //     { error: 'Invalid webhook signature' },
        //     { status: 401 }
        //   );
        // }

        const body = await request.json();
        const { event, data } = body;

        // Handle different webhook events
        switch (event) {
            case 'usage.threshold_reached':
                await handleUsageThreshold(data);
                break;

            case 'usage.limit_exceeded':
                await handleUsageLimitExceeded(data);
                break;

            case 'usage.reset':
                await handleUsageReset(data);
                break;

            default:
                console.warn(`Unhandled webhook event: ${event}`);
                return NextResponse.json(
                    { message: 'Event not handled' },
                    { status: 200 }
                );
        }

        return NextResponse.json({
            message: 'Webhook processed successfully',
        });

    } catch (error) {
        console.error('Error processing webhook:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Helper functions for handling different webhook events
async function handleUsageThreshold(data: any) {
    const { userId, action, currentUsage, threshold } = data;

    // Get current subscription
    const currentSubscription = await db
        .select()
        .from(subscription)
        .where(eq(subscription.userId, userId))
        .limit(1);

    if (currentSubscription.length === 0) {
        throw new Error('No active subscription found');
    }

    // Here you would typically:
    // 1. Send notification to user
    // 2. Update subscription status if needed
    // 3. Trigger any necessary business logic

    console.log(`Usage threshold reached for user ${userId}: ${action} (${currentUsage}/${threshold})`);
}

async function handleUsageLimitExceeded(data: any) {
    const { userId, action, currentUsage, limit } = data;

    // Get current subscription
    const currentSubscription = await db
        .select()
        .from(subscription)
        .where(eq(subscription.userId, userId))
        .limit(1);

    if (currentSubscription.length === 0) {
        throw new Error('No active subscription found');
    }

    // Update subscription status to indicate limit exceeded
    await db
        .update(subscription)
        .set({
            status: 'limit_exceeded',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));

    // Here you would typically:
    // 1. Send notification to user
    // 2. Trigger upgrade flow
    // 3. Implement any necessary restrictions

    console.log(`Usage limit exceeded for user ${userId}: ${action} (${currentUsage}/${limit})`);
}

async function handleUsageReset(data: any) {
    const { userId } = data;

    // Get current subscription
    const currentSubscription = await db
        .select()
        .from(subscription)
        .where(eq(subscription.userId, userId))
        .limit(1);

    if (currentSubscription.length === 0) {
        throw new Error('No active subscription found');
    }

    // Reset usage for the current period
    const currentDate = new Date();
    const currentPeriod = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    await db
        .update(subscriptionUsage)
        .set({
            messageCount: 0,
            widgetCount: 0,
            updatedAt: new Date()
        })
        .where(
            and(
                eq(subscriptionUsage.userId, userId),
                eq(subscriptionUsage.period, currentPeriod)
            )
        );

    // Reset subscription status if it was limit exceeded
    if (currentSubscription[0].status === 'limit_exceeded') {
        await db
            .update(subscription)
            .set({
                status: 'active',
                updatedAt: new Date(),
            })
            .where(eq(subscription.userId, userId));
    }

    console.log(`Usage reset for user ${userId}`);
} 