"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useWidgetsStore } from "@/store/useWidgetsStore";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/Card";
import { ArrowLeft } from "lucide-react";
import Widget from "@/components/Widget";
import type { WidgetFormValues } from "@/lib/validations/widget";

export default function PreviewWidgetPage() {
  const params = useParams();
  const router = useRouter();
  const { widgets } = useWidgetsStore();
  const [isLoading, setIsLoading] = useState(true);

  const widget = widgets.find((w) => w.id === params.id) as WidgetFormValues & {
    id: string;
  };

  useEffect(() => {
    if (!widget) {
      router.push("/dashboard/widgets");
    } else {
      setIsLoading(false);
    }
  }, [widget, router]);

  if (isLoading || !widget) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/dashboard/widgets")}
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Preview Widget</h1>
          <p className="text-muted-foreground mt-1">
            See how your widget looks on your website
          </p>
        </div>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">{widget.name}</h3>
              <p className="text-sm text-muted-foreground">
                Position: {widget.position}
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/widgets/${widget.id}`)}
            >
              Edit Widget
            </Button>
          </div>

          <div className="relative h-[600px] border rounded-lg bg-gray-50">
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-gray-500">Website Preview</p>
            </div>
            <Widget
              widgetId={widget.id}
              position={widget.position}
              primaryColor={widget.primaryColor}
              productName={widget.productName}
              productType={widget.productType}
              features={widget.features}
              description={widget.description}
              faqs={widget.faqs}
              widgetTitle={widget.widgetTitle}
              welcomeMessage={widget.welcomeMessage}
              feedbackQuestion={widget.feedbackQuestion}
              enableBugReports={widget.enableBugReports}
            />
          </div>

          <div className="pt-4 border-t">
            <h4 className="font-medium mb-2">Embed Code</h4>
            <div className="bg-gray-100 p-4 rounded-lg">
              <code className="text-sm">
                {`<script src="https://betterfaq.ai/widget.js" data-widget-id="${widget.id}"></script>`}
              </code>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
