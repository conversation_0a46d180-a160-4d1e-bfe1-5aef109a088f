export const theme = {
    colors: {
        background: {
            primary: '#0A0F1C',
            secondary: '#111827',
            tertiary: '#1F2937',
        },
        text: {
            primary: '#F3F4F6',
            secondary: '#9CA3AF',
            accent: '#60A5FA',
        },
        accent: {
            primary: '#3B82F6',
            secondary: '#60A5FA',
            tertiary: '#93C5FD',
        },
        border: {
            primary: '#374151',
            secondary: '#4B5563',
        },
        success: {
            primary: '#10B981',
            secondary: '#34D399',
        },
        error: {
            primary: '#EF4444',
            secondary: '#F87171',
        },
        warning: {
            primary: '#F59E0B',
            secondary: '#FBBF24',
        },
    },
    spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem',
        '3xl': '4rem',
    },
    borderRadius: {
        sm: '0.25rem',
        md: '0.5rem',
        lg: '1rem',
        full: '9999px',
    },
    shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    },
    typography: {
        fontFamily: {
            sans: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            mono: 'JetBrains Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
        },
        fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            base: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            '2xl': '1.5rem',
            '3xl': '1.875rem',
            '4xl': '2.25rem',
        },
        fontWeight: {
            normal: '400',
            medium: '500',
            semibold: '600',
            bold: '700',
        },
    },
    transitions: {
        default: 'all 0.2s ease-in-out',
        fast: 'all 0.1s ease-in-out',
        slow: 'all 0.3s ease-in-out',
    },
    zIndex: {
        base: '0',
        dropdown: '1000',
        sticky: '1020',
        fixed: '1030',
        modal: '1040',
        popover: '1050',
        tooltip: '1060',
    },
} as const; 