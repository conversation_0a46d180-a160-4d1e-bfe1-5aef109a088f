import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { subscription, subscriptionUsage } from '@/db/schema';
import { eq, and, gte, lte } from 'drizzle-orm';

// GET - Get subscription details
export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;

        // Get current subscription
        const currentSubscription = await db
            .select()
            .from(subscription)
            .where(eq(subscription.userId, userId))
            .limit(1);

        if (currentSubscription.length === 0) {
            return NextResponse.json({
                subscription: null,
                message: 'No active subscription found',
            });
        }

        // Get usage data for current billing period
        const now = new Date();
        const currentPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

        const usageData = await db
            .select()
            .from(subscriptionUsage)
            .where(
                and(
                    eq(subscriptionUsage.userId, userId),
                    eq(subscriptionUsage.period, currentPeriod)
                )
            );

        // Calculate usage summary
        const usageSummary = usageData.length > 0 ? {
            messageCount: usageData[0].messageCount,
            widgetCount: usageData[0].widgetCount
        } : {
            messageCount: 0,
            widgetCount: 0
        };

        return NextResponse.json({
            subscription: currentSubscription[0],
            usage: usageSummary,
            message: 'Subscription details retrieved successfully',
        });

    } catch (error) {
        console.error('Error fetching subscription:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// POST - Update subscription
export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;
        const body = await request.json();
        const { planId, paymentMethodId } = body;

        if (!planId || !paymentMethodId) {
            return NextResponse.json(
                { error: 'Plan ID and payment method ID are required' },
                { status: 400 }
            );
        }

        // Here you would typically:
        // 1. Validate the plan exists and is available
        // 2. Process payment with your payment provider
        // 3. Create or update the subscription
        // 4. Handle any necessary webhook registrations

        // For now, we'll simulate a successful subscription update
        const currentPeriodStart = new Date();
        const currentPeriodEnd = new Date();
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1); // 1 month subscription

        const newSubscription = await db
            .insert(subscription)
            .values({
                userId,
                plan: planId,
                currentPeriodStart,
                currentPeriodEnd,
                status: 'active',
            })
            .returning();

        return NextResponse.json({
            subscription: newSubscription[0],
            message: 'Subscription updated successfully',
        });

    } catch (error) {
        console.error('Error updating subscription:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// DELETE - Cancel subscription
export async function DELETE(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;

        // Here you would typically:
        // 1. Cancel the subscription with your payment provider
        // 2. Update the subscription status in your database
        // 3. Handle any necessary webhook deregistrations

        // For now, we'll simulate a successful cancellation
        await db
            .update(subscription)
            .set({ status: 'cancelled' })
            .where(eq(subscription.userId, userId));

        return NextResponse.json({
            message: 'Subscription cancelled successfully',
        });

    } catch (error) {
        console.error('Error cancelling subscription:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
} 