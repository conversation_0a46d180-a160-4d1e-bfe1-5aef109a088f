# Neon Database
DATABASE_URL="****************************************************"

# NextAuth.js Credentials
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
# Generate a strong secret using: openssl rand -base64 32
NEXTAUTH_SECRET="YOUR_STRONG_RANDOM_SECRET"

# Polar.sh Integration
POLAR_SECRET_KEY="sk_test_your_polar_secret_key"
POLAR_WEBHOOK_SECRET="whsec_your_polar_webhook_secret"

# AI STUFF - Groq
GROQ_API_KEY="your_groq_api_key"

NEXT_PUBLIC_APP_URL="http://localhost:3000"
