'use server';

import { db } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { revalidatePath } from 'next/cache';
import type { WidgetFormValues } from '@/lib/validations/widget';
import { authOptions } from '@/lib/auth';
import { and, eq } from 'drizzle-orm';
import { widget } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';

export async function createWidget(data: WidgetFormValues) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    const newWidget = await db.insert(widget).values({
        id: uuidv4(),
        userId: session.user.id,
        name: data.name,
        position: data.position,
        primaryColor: data.primaryColor,
        productType: data.productType,
        productName: data.productName,
        features: data.features,
        description: data.description,
        faqs: data.faqs,
        widgetTitle: data.widgetTitle,
        welcomeMessage: data.welcomeMessage,
        feedbackQuestion: data.feedbackQuestion,
        enableBugReports: data.enableBugReports,
        isActive: data.isActive,
        createdAt: new Date(),
        updatedAt: new Date()
    }).returning();

    revalidatePath('/dashboard/widgets');
    return newWidget[0];
}

export async function updateWidget(id: string, data: Partial<WidgetFormValues>) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    const updateData: any = {
        updatedAt: new Date()
    };

    // Only include fields that are provided
    if (data.name !== undefined) updateData.name = data.name;
    if (data.position !== undefined) updateData.position = data.position;
    if (data.primaryColor !== undefined) updateData.primaryColor = data.primaryColor;
    if (data.productType !== undefined) updateData.productType = data.productType;
    if (data.productName !== undefined) updateData.productName = data.productName;
    if (data.features !== undefined) updateData.features = data.features;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.faqs !== undefined) updateData.faqs = data.faqs;
    if (data.widgetTitle !== undefined) updateData.widgetTitle = data.widgetTitle;
    if (data.welcomeMessage !== undefined) updateData.welcomeMessage = data.welcomeMessage;
    if (data.feedbackQuestion !== undefined) updateData.feedbackQuestion = data.feedbackQuestion;
    if (data.enableBugReports !== undefined) updateData.enableBugReports = data.enableBugReports;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    const updatedWidget = await db.update(widget)
        .set(updateData)
        .where(and(
            eq(widget.id, id),
            eq(widget.userId, session.user.id)
        ))
        .returning();

    revalidatePath('/dashboard/widgets');
    revalidatePath(`/dashboard/widgets/${id}`);
    return updatedWidget[0];
}

export async function deleteWidget(id: string) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
        throw new Error('Unauthorized');
    }

    await db.delete(widget)
        .where(and(
            eq(widget.id, id),
            eq(widget.userId, session.user.id)
        ));

    revalidatePath('/dashboard/widgets');
}

function generateApiKey() {
    return 'sk_' + Array.from({ length: 32 }, () =>
        Math.floor(Math.random() * 16).toString(16)
    ).join('');
} 