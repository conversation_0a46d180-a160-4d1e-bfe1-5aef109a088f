"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import {
  ArrowRight,
  MessageSquare,
  Zap,
  Shield,
  BarChart,
  Menu,
  X,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import Navbar from "@/components/navbar";

export default function LandingPage() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation - Simplified */}
      <Navbar />

      {/* Hero Section - More Spacious */}
      <section className="pt-40 pb-28 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-indigo-50 to-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
                AI-Powered Support ✨
              </Badge>
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-8 leading-tight text-gray-800">
                Double Your Conversions with{" "}
                <span className="bg-gradient-to-r from-indigo-700 to-indigo-500 bg-clip-text text-transparent">
                  Instant Answers
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-10 font-medium">
                Our AI-powered FAQ widget answers customer questions instantly,
                builds confidence, and increases purchase rates by 2x. 🚀
              </p>
              <div className="flex flex-col sm:flex-row gap-5">
                <Link href="/signup">
                  <Button
                    size="lg"
                    className="bg-indigo-700 hover:bg-indigo-800 text-white font-semibold text-lg"
                  >
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="#demo">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-indigo-600 text-indigo-700 hover:bg-indigo-50 font-semibold text-lg"
                  >
                    See Demo
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-indigo-300 rounded-full blur-[120px] opacity-30" />
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                <div className="p-6 border-b border-gray-100 bg-gray-50">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    <div className="ml-4 text-sm text-gray-500">
                      BetterFAQ Widget
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex flex-col space-y-6">
                    <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                      How does your pricing work? 💰
                    </div>
                    <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                      We offer a free plan with 20 messages per month, and our
                      Pro plan at $9/month includes 1000 messages, analytics,
                      and custom branding.
                    </div>
                    <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                      Can I customize the widget to match my brand?
                    </div>
                    <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                      Yes! Our Pro plan allows full customization of colors,
                      position, and branding to perfectly match your website's
                      design.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - More Spacious */}
      <section id="features" className="py-28 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
              Features
            </Badge>
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Everything You Need for Exceptional Support
            </h2>
            <p className="text-gray-600 text-xl max-w-3xl mx-auto">
              Our AI-powered FAQ widget provides instant, accurate answers to
              customer questions. 🤖
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <MessageSquare className="w-7 h-7 text-indigo-700" />
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  AI-Powered Responses 🧠
                </h3>
                <p className="text-gray-600">
                  Our advanced AI understands context and provides accurate,
                  helpful responses to customer questions in real-time.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <Zap className="w-7 h-7 text-indigo-700" />
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  Lightning Fast ⚡
                </h3>
                <p className="text-gray-600">
                  Get instant answers to customer questions, reducing wait times
                  and improving satisfaction and conversion rates.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <Shield className="w-7 h-7 text-indigo-700" />
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  Secure & Reliable 🔒
                </h3>
                <p className="text-gray-600">
                  Enterprise-grade security and reliability to protect your data
                  and ensure 99.9% uptime for your customers.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <BarChart className="w-7 h-7 text-indigo-700" />
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  Detailed Analytics 📊
                </h3>
                <p className="text-gray-600">
                  Track customer interactions, identify common questions, and
                  optimize your support strategy with actionable insights.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="w-7 h-7 text-indigo-700"
                  >
                    <path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path>
                    <path d="M7 7h.01"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  Custom Branding 🎨
                </h3>
                <p className="text-gray-600">
                  Customize the widget's appearance to match your brand with
                  custom colors, logos, and positioning options.
                </p>
              </CardContent>
            </Card>

            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="pt-8 pb-8">
                <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="w-7 h-7 text-indigo-700"
                  >
                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                    <path d="m2 12 5.25 5 2.25-3H19l-4-6-4 6h-3.5l-2.25 3L2 12z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  Easy Integration 🔌
                </h3>
                <p className="text-gray-600">
                  Add the widget to your website with a simple code snippet. No
                  coding experience required.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Demo Section - More Spacious */}
      <section id="demo" className="py-28 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
                Live Demo
              </Badge>
              <h2 className="text-3xl sm:text-4xl font-bold mb-6">
                See BetterFAQ AI in Action ✨
              </h2>
              <p className="text-gray-600 text-xl mb-8">
                Experience how our AI-powered FAQ widget can transform your
                customer support and increase conversions. Try asking a
                question!
              </p>
              <ul className="space-y-6 mb-10">
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700 text-lg">
                    <strong>Instant Responses:</strong> Get immediate answers to
                    customer questions
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700 text-lg">
                    <strong>Contextual Understanding:</strong> Our AI
                    understands the intent behind questions
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700 text-lg">
                    <strong>24/7 Availability:</strong> Support your customers
                    around the clock
                  </span>
                </li>
              </ul>
              <Link href="/signup">
                <Button
                  size="lg"
                  className="bg-indigo-700 hover:bg-indigo-800 text-white"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
            <div className="relative">
              <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-indigo-300 rounded-full blur-[120px] opacity-30" />
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                <div className="p-6 border-b border-gray-100 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
                      <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Interactive Demo
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex flex-col space-y-6">
                    <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                      What makes BetterFAQ different from other FAQ solutions?
                      🤔
                    </div>
                    <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                      Unlike traditional FAQ systems that only show pre-written
                      answers, BetterFAQ uses AI to understand questions in
                      context and provide personalized responses. Our solution
                      learns from interactions to continuously improve, and
                      offers detailed analytics to help optimize your support
                      strategy.
                    </div>
                    <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                      How long does it take to set up? ⏱️
                    </div>
                    <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                      Most customers are up and running in less than 5 minutes!
                      Simply sign up, customize your widget, and add our code
                      snippet to your website. No technical expertise required.
                    </div>
                  </div>
                  <div className="mt-8 relative">
                    <input
                      type="text"
                      placeholder="Ask a question..."
                      className="w-full p-4 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                    <Button
                      size="sm"
                      className="absolute right-2 top-2 bg-indigo-700 hover:bg-indigo-800"
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section - Simplified */}
      <section id="pricing" className="py-28 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
              Pricing
            </Badge>
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Simple, Transparent Pricing 💰
            </h2>
            <p className="text-gray-600 text-xl max-w-3xl mx-auto">
              Choose the plan that's right for your business. All plans include
              our core AI features.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <Card className="border-gray-200 hover:shadow-lg transition-shadow">
              <CardContent className="p-10">
                <h3 className="text-2xl font-bold mb-2">Free</h3>
                <div className="mb-8">
                  <span className="text-4xl font-bold">$0</span>
                  <span className="text-gray-500">/month</span>
                </div>
                <p className="text-gray-600 mb-8">
                  Perfect for small websites and personal projects.
                </p>
                <ul className="space-y-4 mb-10">
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">1 widget</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">20 messages per month</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">Basic analytics</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">Community support</span>
                  </li>
                </ul>
                <Link href="/signup" className="block w-full">
                  <Button
                    variant="outline"
                    className="w-full border-indigo-600 text-indigo-700 hover:bg-indigo-50"
                  >
                    Get Started
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="border-indigo-500 border-2 hover:shadow-lg transition-shadow relative">
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <Badge className="bg-indigo-700 hover:bg-indigo-800 text-white border-none px-3 py-1">
                  Most Popular
                </Badge>
              </div>
              <CardContent className="p-10">
                <h3 className="text-2xl font-bold mb-2">Pro</h3>
                <div className="mb-8">
                  <span className="text-4xl font-bold">$9</span>
                  <span className="text-gray-500">/month</span>
                </div>
                <p className="text-gray-600 mb-8">
                  Ideal for growing businesses and e-commerce sites.
                </p>
                <ul className="space-y-4 mb-10">
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">10 widget</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">
                      1000 messages per month
                    </span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">Advanced analytics</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">Priority support</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">Custom branding</span>
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-3 text-xl">✓</span>
                    <span className="text-gray-700">API access</span>
                  </li>
                </ul>
                <Link href="/signup" className="block w-full">
                  <Button className="w-full bg-indigo-700 hover:bg-indigo-800 text-white">
                    Get Started
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-28 px-4 sm:px-6 lg:px-8 bg-indigo-700">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-8 text-white">
            Ready to Transform Your Customer Support? 🚀
          </h2>
          <p className="text-indigo-100 text-xl mb-10 max-w-3xl mx-auto">
            Join thousands of businesses using BetterFAQ AI to provide instant
            answers, reduce support tickets, and increase conversions.
          </p>
          <Link href="/signup">
            <Button
              size="lg"
              className="bg-white text-indigo-700 hover:bg-indigo-50"
            >
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer - Simplified */}
      <footer className="bg-white py-12 px-4 sm:px-6 lg:px-8 border-t border-gray-200">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <Link
                href="/"
                className="text-xl font-bold bg-gradient-to-r from-indigo-700 to-indigo-500 bg-clip-text text-transparent"
              >
                BetterFAQ AI
              </Link>
            </div>
            <div className="flex space-x-8 mb-6 md:mb-0">
              <Link
                href="/privacy"
                className="text-gray-600 hover:text-indigo-700"
              >
                Privacy
              </Link>
              <Link
                href="/terms"
                className="text-gray-600 hover:text-indigo-700"
              >
                Terms
              </Link>
              <Link
                href="/contact"
                className="text-gray-600 hover:text-indigo-700"
              >
                Contact
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link
                href="https://twitter.com"
                className="text-gray-500 hover:text-indigo-700"
              >
                <span className="sr-only">Twitter</span>
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </Link>
              <Link
                href="https://github.com"
                className="text-gray-500 hover:text-indigo-700"
              >
                <span className="sr-only">GitHub</span>
                <svg
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                    clipRule="evenodd"
                  />
                </svg>
              </Link>
            </div>
          </div>
          <div className="mt-8 text-center text-gray-500 text-sm">
            <p>
              &copy; {new Date().getFullYear()} BetterFAQ AI. All rights
              reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
