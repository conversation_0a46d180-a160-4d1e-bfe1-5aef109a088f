import { useState } from 'react';

interface FeedbackFormProps {
  widgetId: string;
  onClose: () => void;
}

export default function FeedbackForm({ widgetId, onClose }: FeedbackFormProps) {
  const [type, setType] = useState<'question' | 'bug' | 'agent'>('question');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          widgetId,
          type,
          content,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      onClose();
    } catch (error) {
      console.error('Failed to submit feedback. Please try again.', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Send Feedback</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Type</label>
          <select
            value={type}
            onChange={(e) => setType(e.target.value as unknown as 'question' | 'bug' | 'agent')}
            className="w-full p-2 border rounded-lg bg-background-primary border-border-primary"
          >
            <option value="question">Question</option>
            <option value="bug">Bug Report</option>
            <option value="agent">Talk to Agent</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Message</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
            className="w-full p-2 border rounded-lg bg-background-primary border-border-primary"
            placeholder="Type your message here..."
            required
          />
        </div>

        <div className="flex justify-end gap-2">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm border rounded-lg hover:bg-background-tertiary"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm bg-accent-primary text-white rounded-lg hover:bg-accent-secondary disabled:opacity-50"
          >
            {isSubmitting ? 'Sending...' : 'Send Feedback'}
          </button>
        </div>
      </form>
    </div>
  );
} 