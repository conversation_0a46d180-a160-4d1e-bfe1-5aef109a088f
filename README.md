## BetterFAQ AI is a tool designed to help businesses provide instant, accurate answers to customer questions. It uses AI to understand questions in context and provide personalized responses. 

### Pages:

(Public)
"/" 
- The main landing page

(Authentiation)
"/signin" 
- for authentication 

(Dashboard)
"/dashboard"
- Show the dashboard with key metrics and recent activity 
- How many times total messages have been sent
- recent feedbacks (answers by the customers to a question the product owner would ask within the widget)
- bug reports (any general bugs reported by the customers)

"/dashboard/widgets"
- List all widgets the user has created, and button to create widget. 

"/dashboard/widgets/new"
- a form to create a new widget,
- would show the preview of the widget in the right side live before even the user creates
- after creating, should take to the individual widgets page and user would copy the code

"/dashboard/widgets/[id]"
- View and manage a specific widget, including all the analytics, feedbacks and bug reports that has come from this widget
- edit the widgets configuration
- code snippet to be copied with guide on how to use it
- a preview of the widgets UI in a box
- ability to delete the widget

"/dashboard/settings"
- Manage users plan
- and see the usage metrics: how many widgets created how many remains, how many messages how many remains.
- An upgrade button to upgarde their plan from free to the pro plan
- if the user has pro plan then ability to cancel
- seeing how much longer their plans will last

"/purchase-success"
- a succes page after user buys the pro plan


### Details on the widget (agent):
- Customers would see a small agent icon with a text set by the widget owner. something like "Talk with me!"
- once clicked, a small interface would slowly popup there, which would have a welcome message on top like "Help us by sharing your feedback!" 
- another option woild show for feedback, which would take to the single question and answer field and would show a thank you upon answering the question
- another option would be to report any bug 
- another one for a feature request
- another one to talk to the agent
- all these options would be inside the small interface on top of each other, and the interface would be very nice ui, bold modern type, with a top section with a solid color (set by the widgets configuration)

- the customers would click on them, and provide feedback, bug report, feature request etc
- and for the agent system, they would have a nice chat interface right in there. which the customers could talk to for clearifying anything. and the agent would have complete context and information about the product. set by the product owner in our dashboard widget settings.


## Technical Specification:

### General:
- Next.js 15
- React
- Tailwind CSS v4
- Zustand for state management
- Shadcn UI for components
- Next auth with OAuth (Google & Github)
- Drizzle ORM for database queries
- Groq for AI model
- Polar.sh for payments management
- NeonDB for database

### Server:
- All actions through api routes and server actions

### File structure:
- Every hook/stores inside the stores folder
- all utils inside the lib folder
- all components inside the components folder.
with sub directories for each of the page groups

like components/dashboard/widgets would have all widgets components
- files shouldnt be larger than 300-400 max. split into multple files and components. 
- Following component composition
- Single Responsibility principle

### Color palet : 
- Light mode. with indigo colors.