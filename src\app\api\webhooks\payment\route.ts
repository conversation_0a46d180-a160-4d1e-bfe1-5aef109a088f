import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { subscription } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Handle payment webhook events
export async function POST(request: NextRequest) {
    try {
        // Verify webhook signature (implement based on your payment provider)
        // const signature = request.headers.get('x-webhook-signature');
        // if (!verifyWebhookSignature(request.body, signature)) {
        //   return NextResponse.json(
        //     { error: 'Invalid webhook signature' },
        //     { status: 401 }
        //   );
        // }

        const body = await request.json();
        const { event, data } = body;

        // Handle different webhook events
        switch (event) {
            case 'payment.succeeded':
                await handlePaymentSuccess(data);
                break;

            case 'payment.failed':
                await handlePaymentFailure(data);
                break;

            case 'payment.refunded':
                await handlePaymentRefund(data);
                break;

            case 'payment.dispute.created':
                await handlePaymentDispute(data);
                break;

            default:
                console.warn(`Unhandled webhook event: ${event}`);
                return NextResponse.json(
                    { message: 'Event not handled' },
                    { status: 200 }
                );
        }

        return NextResponse.json({
            message: 'Webhook processed successfully',
        });

    } catch (error) {
        console.error('Error processing webhook:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Helper functions for handling different webhook events
async function handlePaymentSuccess(data: any) {
    const { userId, subscriptionId, amount, currency } = data;

    // Update subscription status
    await db
        .update(subscription)
        .set({
            status: 'active',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));

    // Here you would typically:
    // 1. Send payment confirmation email
    // 2. Update payment records
    // 3. Trigger any necessary business logic

    console.log(`Payment succeeded for user ${userId}: ${amount} ${currency}`);
}

async function handlePaymentFailure(data: any) {
    const { userId, subscriptionId, error } = data;

    // Update subscription status
    await db
        .update(subscription)
        .set({
            status: 'payment_failed',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));

    // Here you would typically:
    // 1. Send payment failure notification
    // 2. Update payment records
    // 3. Trigger retry logic if applicable

    console.log(`Payment failed for user ${userId}: ${error}`);
}

async function handlePaymentRefund(data: any) {
    const { userId, subscriptionId, amount, currency, reason } = data;

    // Update subscription status
    await db
        .update(subscription)
        .set({
            status: 'refunded',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));

    // Here you would typically:
    // 1. Send refund confirmation
    // 2. Update payment records
    // 3. Handle any necessary access restrictions

    console.log(`Payment refunded for user ${userId}: ${amount} ${currency} - ${reason}`);
}

async function handlePaymentDispute(data: any) {
    const { userId, subscriptionId, disputeId, reason } = data;

    // Update subscription status
    await db
        .update(subscription)
        .set({
            status: 'disputed',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));

    // Here you would typically:
    // 1. Send dispute notification
    // 2. Update payment records
    // 3. Handle any necessary access restrictions
    // 4. Prepare for dispute resolution

    console.log(`Payment dispute created for user ${userId}: ${reason}`);
} 