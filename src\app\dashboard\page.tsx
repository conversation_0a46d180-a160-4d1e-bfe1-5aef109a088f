"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/Badge";
import { formatDistanceToNow } from "date-fns";
import {
  MessageSquare,
  ThumbsUp,
  Bug,
  WorkflowIcon as Widgets,
  Plus,
  ExternalLink,
  TrendingUp,
} from "lucide-react";

interface DashboardData {
  stats: {
    totalMessages: number;
    totalFeedbacks: number;
    totalBugReports: number;
    activeWidgets: number;
    growth: {
      messages: number;
      feedbacks: number;
      bugReports: number;
    };
  };
  recentFeedbacks: Array<{
    id: string;
    widgetId: string;
    widgetName: string;
    content: string;
    createdAt: Date;
  }>;
  recentBugReports: Array<{
    id: string;
    widgetId: string;
    widgetName: string;
    title: string;
    createdAt: Date;
  }>;
}

export default function DashboardPage() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Fetch real dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/dashboard/analytics");
        if (response.ok) {
          const dashboardData = await response.json();
          // Convert date strings to Date objects
          dashboardData.recentFeedbacks = dashboardData.recentFeedbacks.map(
            (f: any) => ({
              ...f,
              createdAt: new Date(f.createdAt),
            })
          );
          dashboardData.recentBugReports = dashboardData.recentBugReports.map(
            (b: any) => ({
              ...b,
              createdAt: new Date(b.createdAt),
            })
          );
          setData(dashboardData);
        } else {
          console.error("Failed to fetch dashboard data");
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const handleNavigateToWidgets = () => {
    router.push("/dashboard/widgets");
  };

  const handleNavigateToWidget = (widgetId: string) => {
    router.push(`/dashboard/widgets/${widgetId}`);
  };

  const handleCreateWidget = () => {
    router.push("/dashboard/widgets/new");
  };

  if (isLoading) {
    return (
      <div className="space-y-8 p-6">
        {/* Stats Cards Skeleton */}
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <Skeleton className="h-4 w-24" />
                </CardTitle>
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Widgets Section Skeleton */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              {[...Array(2)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      <Skeleton className="h-5 w-32" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[...Array(3)].map((_, j) => (
                        <div key={j} className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-3 w-16" />
                            <Skeleton className="h-3 w-20" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-8 p-6">
      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Messages
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.stats.totalMessages.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
              <TrendingUp
                className={`h-3 w-3 ${
                  data.stats.growth.messages >= 0 ? "" : "rotate-180"
                }`}
              />
              {data.stats.growth.messages >= 0 ? "+" : ""}
              {data.stats.growth.messages}% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Feedbacks
            </CardTitle>
            <ThumbsUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.stats.totalFeedbacks}
            </div>
            <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
              <TrendingUp
                className={`h-3 w-3 ${
                  data.stats.growth.feedbacks >= 0 ? "" : "rotate-180"
                }`}
              />
              {data.stats.growth.feedbacks >= 0 ? "+" : ""}
              {data.stats.growth.feedbacks}% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Bug Reports
            </CardTitle>
            <Bug className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.stats.totalBugReports}
            </div>
            <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
              <TrendingUp
                className={`h-3 w-3 ${
                  data.stats.growth.bugReports >= 0 ? "" : "rotate-180"
                }`}
              />
              {data.stats.growth.bugReports >= 0 ? "+" : ""}
              {data.stats.growth.bugReports}% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Widgets Section */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Widgets className="h-5 w-5 text-purple-600" />
              <CardTitle className="text-xl">Active Widgets</CardTitle>
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-700"
              >
                {data.stats.activeWidgets}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNavigateToWidgets}
              className="flex items-center gap-1"
            >
              View All
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.stats.activeWidgets === 0 ? (
            <div className="text-center py-12">
              <Widgets className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No widgets created yet
              </h3>
              <p className="text-muted-foreground mb-4">
                Create your first widget to start collecting feedback and
                messages.
              </p>
              <Button
                onClick={handleCreateWidget}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Widget
              </Button>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Recent Feedbacks */}
              <Card className="border-l-4 border-l-green-500">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <ThumbsUp className="h-4 w-4 text-green-600" />
                    Recent Feedbacks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.recentFeedbacks.map((feedback) => (
                      <div
                        key={feedback.id}
                        className="p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() =>
                          handleNavigateToWidget(feedback.widgetId)
                        }
                      >
                        <div className="flex items-start justify-between mb-2">
                          <Badge
                            variant="default"
                            className="bg-blue-100 text-blue-700"
                          >
                            Feedback
                          </Badge>
                          <ExternalLink className="h-3 w-3 text-muted-foreground" />
                        </div>
                        <p className="text-sm font-medium mb-1 line-clamp-2">
                          {feedback.content}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{feedback.widgetName}</span>
                          <span>
                            {formatDistanceToNow(feedback.createdAt, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Bug Reports */}
              <Card className="border-l-4 border-l-red-500">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Bug className="h-4 w-4 text-red-600" />
                    Recent Bug Reports
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.recentBugReports.map((bug) => (
                      <div
                        key={bug.id}
                        className="p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => handleNavigateToWidget(bug.widgetId)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <Badge
                            variant="destructive"
                            className="bg-red-100 text-red-700"
                          >
                            Bug Report
                          </Badge>
                          <ExternalLink className="h-3 w-3 text-muted-foreground" />
                        </div>
                        <p className="text-sm font-medium mb-1 line-clamp-2">
                          {bug.title}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{bug.widgetName}</span>
                          <span>
                            {formatDistanceToNow(bug.createdAt, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
