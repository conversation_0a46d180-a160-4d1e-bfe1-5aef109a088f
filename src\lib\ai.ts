import { Groq } from 'groq-sdk';

if (!process.env.GROQ_API_KEY) {
    throw new Error('GROQ_API_KEY is not set');
}

export const groq = new Groq({
    apiKey: process.env.GROQ_API_KEY,
});

export const AI_MODEL = 'meta-llama/llama-4-maverick-17b-128e-instruct';

// Define the widget type based on your database schema
// Note: Nullable fields in the database return null, not undefined
type WidgetData = {
  id: string;
  userId: string;
  name: string;
  position: string;
  primaryColor: string;
  productType: string;
  productName: string;
  features: string[] | null;
  description: string;
  faqs: Array<{
    question: string;
    answer: string;
  }> | null;
  widgetTitle: string;
  welcomeMessage: string;
  feedbackQuestion: string | null;
  enableBugReports: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export async function generateAIResponse(
  message: string,
  widgetData: WidgetData
) {
  // Build context from widget data
  let context = '';

  if (widgetData.productName) {
    context += `Product: ${widgetData.productName}\n`;
  }

  if (widgetData.productType) {
    context += `Product Type: ${widgetData.productType}\n`;
  }

  if (widgetData.description) {
    context += `Product Description: ${widgetData.description}\n`;
  }

  if (widgetData.features && widgetData.features.length > 0) {
    context += `Product Features: ${widgetData.features.join(', ')}\n`;
  }

  if (widgetData.faqs && widgetData.faqs.length > 0) {
    context += `Frequently Asked Questions:\n`;
    widgetData.faqs.forEach(qa => {
      context += `Q: ${qa.question}\nA: ${qa.answer}\n`;
    });
  }

  const systemPrompt = `You are a helpful AI assistant for ${widgetData.productName || 'this product'}'s customer support widget.
You should provide helpful, accurate, and friendly responses to customer inquiries.

${context ? `Here is important information about the product/service:\n${context}` : ''}

Guidelines:
- Be helpful and professional
- If you don't know something specific about the product, be honest about it
- For complex issues, suggest contacting customer support
- Keep responses concise but informative
- Use the FAQ information as reference when relevant
- Always maintain a friendly and supportive tone`;

  const completion = await groq.chat.completions.create({
    messages: [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: message,
      },
    ],
    model: AI_MODEL,
    temperature: 0.7,
    max_tokens: 1024,
  });

  return completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
} 