import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { db } from "@/db";
import { user, widget, feedback } from "@/db/schema";
import { eq, and, desc, sql, inArray } from "drizzle-orm";

export async function GET() {
    try {
        const session = await getServerSession();
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // Get user data
        const userData = await db.query.user.findFirst({
            where: eq(user.email, session.user.email),
        });

        if (!userData) {
            return NextResponse.json(
                { error: "User not found" },
                { status: 404 }
            );
        }

        // Get user's widget
        const userwidget = await db.query.widget.findMany({
            where: eq(widget.userId, userData.id),
        });

        const widgetIds = userwidget.map(w => w.id);

        if (widgetIds.length === 0) {
            return NextResponse.json({
                totalMessages: 0,
                totalFeedback: 0,
                positiveFeedbackRate: 0,
                averageResponseTime: 0,
                recentMessages: [],
                recentFeedback: [],
                widgettats: [],
            });
        }

        // Get total messages and feedback
        const [totalMessages, totalFeedback] = await Promise.all([
            db.select({ count: sql<number>`count(*)` })
                .from(feedback)
                .where(inArray(feedback.widgetId, widgetIds)),
            db.select({ count: sql<number>`count(*)` })
                .from(feedback)
                .where(inArray(feedback.widgetId, widgetIds)),
        ]);

        // Get recent messages (last 5)
        const recentMessages = await db.select()
            .from(feedback)
            .where(inArray(feedback.widgetId, widgetIds))
            .orderBy(desc(feedback.createdAt))
            .limit(5);

        // Get recent feedback (last 5)
        const recentFeedback = await db.select()
            .from(feedback)
            .where(inArray(feedback.widgetId, widgetIds))
            .orderBy(desc(feedback.createdAt))
            .limit(5);

        // Calculate widget-specific stats
        const widgettats = await Promise.all(
            userwidget.map(async (widget) => {
                const [messageCount, feedbackCount] = await Promise.all([
                    db.select({ count: sql<number>`count(*)` })
                        .from(feedback)
                        .where(eq(feedback.widgetId, widget.id)),
                    db.select({ count: sql<number>`count(*)` })
                        .from(feedback)
                        .where(eq(feedback.widgetId, widget.id)),
                ]);

                const positiveFeedback = await db.select({ count: sql<number>`count(*)` })
                    .from(feedback)
                    .where(
                        and(
                            eq(feedback.widgetId, widget.id),
                            eq(feedback.type, "positive")
                        )
                    );

                const negativeFeedback = await db.select({ count: sql<number>`count(*)` })
                    .from(feedback)
                    .where(
                        and(
                            eq(feedback.widgetId, widget.id),
                            eq(feedback.type, "negative")
                        )
                    );

                const totalFeedbackCount = feedbackCount[0].count;
                const positiveFeedbackCount = positiveFeedback[0].count;
                const positiveFeedbackRate = totalFeedbackCount > 0
                    ? (positiveFeedbackCount / totalFeedbackCount) * 100
                    : 0;

                return {
                    widgetId: widget.id,
                    name: widget.name,
                    totalMessages: messageCount[0].count,
                    totalFeedback: totalFeedbackCount,
                    positiveFeedback: positiveFeedbackCount,
                    negativeFeedback: negativeFeedback[0].count,
                    averageResponseTime: 0, // TODO: Implement response time tracking
                };
            })
        );

        // Calculate overall stats
        const positiveFeedbackCount = await db.select({ count: sql<number>`count(*)` })
            .from(feedback)
            .where(
                and(
                    inArray(feedback.widgetId, widgetIds),
                    eq(feedback.type, "positive")
                )
            );

        const positiveFeedbackRate = totalFeedback[0].count > 0
            ? (positiveFeedbackCount[0].count / totalFeedback[0].count) * 100
            : 0;

        return NextResponse.json({
            totalMessages: totalMessages[0].count,
            totalFeedback: totalFeedback[0].count,
            positiveFeedbackRate,
            averageResponseTime: 0, // TODO: Implement response time tracking
            recentMessages,
            recentFeedback,
            widgettats,
        });
    } catch (error) {
        console.error("Error fetching dashboard data:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 