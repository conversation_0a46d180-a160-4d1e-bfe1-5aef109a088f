import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { analytics, widget } from '@/db/schema';
import { eq, and, gte, lte, desc, or } from 'drizzle-orm';

// GET - Get analytics data
export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;
        const { searchParams } = new URL(request.url);

        // Get query parameters
        const widgetId = searchParams.get('widgetId');
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const eventType = searchParams.get('eventType');

        // Build query conditions
        const conditions = [];

        // Filter by widget ID
        if (widgetId) {
            // Verify widget belongs to user
            const userWidget = await db
                .select({ id: widget.id })
                .from(widget)
                .where(eq(widget.id, widgetId))
                .limit(1);

            if (userWidget.length === 0) {
                return NextResponse.json(
                    { error: 'Widget not found' },
                    { status: 404 }
                );
            }
            conditions.push(eq(analytics.widgetId, widgetId));
        } else {
            // If no widget ID specified, get all user's widgets
            const userWidgets = await db
                .select({ id: widget.id })
                .from(widget)
                .where(eq(widget.userId, userId));

            const widgetIds = userWidgets.map(w => w.id);
            if (widgetIds.length > 0) {
                // Use OR conditions for each widget ID
                const widgetConditions = widgetIds.map(id => eq(analytics.widgetId, id));
                conditions.push(or(...widgetConditions));
            } else {
                // No widgets found, return empty result
                return NextResponse.json({
                    data: [],
                    summary: { totalEvents: 0, eventTypes: {} },
                    message: 'No widgets found for user',
                });
            }
        }

        // Filter by date range
        if (startDate) {
            conditions.push(gte(analytics.createdAt, new Date(startDate)));
        }
        if (endDate) {
            conditions.push(lte(analytics.createdAt, new Date(endDate)));
        }

        // Filter by event type
        if (eventType) {
            conditions.push(eq(analytics.eventType, eventType));
        }

        // Get analytics data
        const analyticsData = await db
            .select()
            .from(analytics)
            .where(and(...conditions))
            .orderBy(desc(analytics.createdAt));

        // Get summary statistics
        const summary = {
            totalEvents: analyticsData.length,
            eventTypes: analyticsData.reduce((acc, event) => {
                acc[event.eventType] = (acc[event.eventType] || 0) + 1;
                return acc;
            }, {} as Record<string, number>),
            // Add more summary statistics as needed
        };

        return NextResponse.json({
            data: analyticsData,
            summary,
            message: 'Analytics data retrieved successfully',
        });

    } catch (error) {
        console.error('Error fetching analytics:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
} 