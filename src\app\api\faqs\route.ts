import { NextResponse } from 'next/server';
import { db } from '@/db';
import { faq } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(req: Request) {
    try {
        const { searchParams } = new URL(req.url);
        const category = searchParams.get('category');

        const query = category
            ? and(eq(faq.isActive, true), eq(faq.category, category))
            : eq(faq.isActive, true);

        const activefaq = await db.query.faq.findMany({
            where: query,
            orderBy: (faq, { asc }) => [asc(faq.order)],
        });

        return NextResponse.json(activefaq);
    } catch (error) {
        console.error('Error fetching faq:', error);
        return NextResponse.json(
            { error: 'Failed to fetch faq' },
            { status: 500 }
        );
    }
} 