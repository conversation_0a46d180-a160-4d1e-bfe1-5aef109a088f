"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { AlignJustify, X } from "lucide-react";
import { AnimatePresence } from "motion/react";
import * as motion from "motion/react-m";
import { useState, useEffect } from "react";
import { useSession, signIn } from "next-auth/react";
import Image from "next/image";

const settings = {
  navLinks: [
    { name: "features", href: "#features" },
    { name: "pricing", href: "#pricing" },
  ],
  cta: {
    content: "Get Started",
    href: "/signin",
  },
};

// Auth Buttons for Desktop View
function AuthButtonsDesktop() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <Button variant="secondary" disabled>
        Loading...
      </Button>
    );
  }

  if (session) {
    return (
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {session.user?.image && (
            <Image
              src={session.user.image}
              alt={session.user?.name ?? "User"}
              width={32}
              height={32}
              className="rounded-full"
            />
          )}
          <span className="text-sm font-medium hidden lg:inline">
            {session.user?.name?.split(" ")[0] ??
              session.user?.email?.split("@")[0]}
          </span>
        </div>
        <Link href="/dashboard">
          <Button size="sm">Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => signIn("google")}>
        Sign in
      </Button>
      <Link href={settings.cta.href} title={settings.cta.content}>
        <Button size="sm">{settings.cta.content}</Button>
      </Link>
    </div>
  );
}

// Auth Buttons for Mobile View
function AuthButtonsMobile({ toggleMenu }: { toggleMenu: () => void }) {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <Button className="w-full" variant="secondary" disabled>
        Loading...
      </Button>
    );
  }

  if (session) {
    return (
      <div className="flex flex-col space-y-3">
        <div className="flex items-center gap-2">
          {session.user?.image && (
            <Image
              src={session.user.image}
              alt={session.user?.name ?? "User"}
              width={32}
              height={32}
              className="rounded-full"
            />
          )}
          <span className="text-sm font-medium">
            {session.user?.name ?? session.user?.email}
          </span>
        </div>
        <Link href="/dashboard" onClick={toggleMenu}>
          <Button className="w-full">Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-3">
      <Button
        className="w-full"
        variant="secondary"
        onClick={() => {
          signIn("google");
          toggleMenu();
        }}
      >
        Sign in with Google
      </Button>
      <Button
        className="w-full"
        variant="outline"
        onClick={() => {
          signIn("github");
          toggleMenu();
        }}
      >
        Sign in with GitHub
      </Button>
      <Link
        href={settings.cta.href}
        title={settings.cta.content}
        onClick={toggleMenu}
      >
        <Button className="w-full">{settings.cta.content}</Button>
      </Link>
    </div>
  );
}

export default function Navbar() {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isScrolled, setIsScrolled] = useState<boolean>(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 text-xl ${
        isScrolled ? "bg-white shadow-md py-3" : "bg-transparent py-5"
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 sm:px-2">
        <div className="flex justify-between items-center">
          {/* Brand Name */}
          <div className="flex items-center gap-2.5">
            <Image src={"/icons/logo.svg"} alt="" width={50} height={50} />
            <Link
              href="/"
              title="Home"
              id="Logo"
              className="text-3xl font-extrabold bg-gradient-to-r from-indigo-700 to-blue-600 bg-clip-text text-transparent"
            >
              BetterFAQ AI
            </Link>
          </div>

          {/* Nav Links - centered (desktop) */}
          <div className="hidden md:flex items-center justify-center space-x-10">
            {settings.navLinks.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                title={link.name}
                className="text-gray-600 hover:text-indigo-700 font-medium capitalize"
              >
                {link.name}
              </Link>
            ))}
          </div>

          {/* Auth Buttons or CTA - right aligned (desktop) */}
          <div className="hidden md:flex items-center justify-end">
            <AuthButtonsDesktop />
          </div>

          {/* mobile only - burger menu icon */}
          <motion.div
            initial={{ scale: 1, y: 0 }}
            whileTap={{ scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="flex md:hidden cursor-pointer text-gray-700"
            onClick={toggleMenu}
          >
            {!isOpen && <AlignJustify size={24} />}
            {isOpen && <X size={24} />}
          </motion.div>
        </div>
      </div>

      {/* mobile only - menu container with AnimatePresence for exit animations */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="md:hidden bg-white border-t py-4 px-4 sm:px-6 lg:px-8 shadow-lg overflow-hidden"
          >
            <div className="flex flex-col space-y-6">
              <ul className="flex flex-col space-y-2 text-black font-medium select-none text-base">
                {settings.navLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      title={link.name}
                      onClick={toggleMenu}
                      className="block py-2 capitalize text-gray-800 font-semibold hover:text-blue-600"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>

              {/* Auth Buttons for Mobile */}
              <AuthButtonsMobile toggleMenu={toggleMenu} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
