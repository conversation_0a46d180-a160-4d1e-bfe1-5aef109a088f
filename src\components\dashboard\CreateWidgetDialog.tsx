import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  widgetFormSchema,
  type WidgetFormValues,
} from "@/lib/validations/widget";
import { Input } from "@/components/ui/Input";
import { useWidgetsStore } from "@/store/useWidgetsStore";
import { useState } from "react";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/Textarea";

interface CreateWidgetDialogProps {
  children: React.ReactNode;
}

export function CreateWidgetDialog({ children }: CreateWidgetDialogProps) {
  const [open, setOpen] = useState(false);
  const { createWidget } = useWidgetsStore();
  const form = useForm<WidgetFormValues>({
    resolver: zodResolver(widgetFormSchema),
    defaultValues: {
      name: "",
      position: "bottom-right",
      primaryColor: "#6366F1",
      productType: "saas",
      productName: "",
      features: [""],
      description: "",
      faqs: [],
      widgetTitle: "Need Help?",
      welcomeMessage: "How can we help you today?",
      feedbackQuestion: "",
      enableBugReports: true,
      isActive: true,
    },
  });

  const onSubmit = async (data: WidgetFormValues) => {
    try {
      await createWidget(data);
      setOpen(false);
      form.reset();
    } catch (error) {
      // Error is handled by the store
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create Widget</DialogTitle>
          <DialogDescription>
            Create a new FAQ AI widget for your website
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="name">Widget Name</label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="My FAQ Widget"
                className="w-full"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="position">Position</label>
                <select
                  id="position"
                  {...form.register("position")}
                  className="w-full rounded-md border border-input bg-background px-3 py-2"
                >
                  <option value="bottom-right">Bottom Right</option>
                  <option value="bottom-left">Bottom Left</option>
                  <option value="top-right">Top Right</option>
                  <option value="top-left">Top Left</option>
                </select>
              </div>

              <div>
                <label htmlFor="isActive">Status</label>
                <div className="flex items-center space-x-2 mt-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={form.watch("isActive")}
                    onChange={(e) =>
                      form.setValue("isActive", e.target.checked)
                    }
                  />
                  <label htmlFor="isActive">
                    {form.watch("isActive") ? "Active" : "Inactive"}
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="primaryColor">Primary Color</label>
              <div className="flex items-center space-x-2 mt-2">
                <Input
                  id="primaryColor"
                  {...form.register("primaryColor")}
                  type="color"
                  className="h-10 w-20"
                />
                <Input
                  {...form.register("primaryColor")}
                  className="flex-1"
                  placeholder="#6366F1"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="productType">Product Type</label>
                <select
                  id="productType"
                  {...form.register("productType")}
                  className="w-full rounded-md border border-input bg-background px-3 py-2"
                >
                  <option value="saas">SaaS</option>
                  <option value="portfolio">Portfolio</option>
                </select>
              </div>

              <div>
                <label htmlFor="productName">Product Name</label>
                <Input
                  id="productName"
                  {...form.register("productName")}
                  placeholder="Your Amazing Product"
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <label htmlFor="description">Description</label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Describe your product..."
                className="w-full"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              Create Widget
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
