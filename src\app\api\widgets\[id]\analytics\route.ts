import { NextResponse } from "next/server";
import { db } from "@/db";
import { eq, and, gte, lte, sql, desc, count } from "drizzle-orm";
import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";
import { widget, analytics, feedback, widgetData } from "@/db/schema";

export async function GET(
    req: Request,
    context: { params: Promise<{ id: string }> }
) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return new NextResponse("Unauthorized", { status: 401 });
        }

        const params = await context.params;
        const id = params.id;
        const { searchParams } = new URL(req.url);
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");

        // Create date filters
        const dateFilters = [];
        if (startDate) {
            dateFilters.push(gte(analytics.createdAt, new Date(startDate)));
        }
        if (endDate) {
            dateFilters.push(lte(analytics.createdAt, new Date(endDate)));
        }

        // Get widget and verify ownership
        const foundWidget = await db.query.widget.findFirst({
            where: eq(widget.id, id)
        });

        if (!foundWidget || foundWidget.userId !== session.user.id) {
            return new NextResponse("Widget not found", { status: 404 });
        }

        // 1. Total Analytics Events
        const totalEventsQuery = db
            .select({ count: count() })
            .from(analytics)
            .where(and(
                eq(analytics.widgetId, id),
                ...dateFilters
            ));

        const [totalEvents] = await totalEventsQuery;

        // 2. Events by Type
        const eventsByTypeQuery = db
            .select({
                eventType: analytics.eventType,
                count: count()
            })
            .from(analytics)
            .where(and(
                eq(analytics.widgetId, id),
                ...dateFilters
            ))
            .groupBy(analytics.eventType)
            .orderBy(desc(count()));

        const eventsByType = await eventsByTypeQuery;

        // 3. Total Feedback Count
        const feedbackFilters = [];
        if (startDate) {
            feedbackFilters.push(gte(feedback.createdAt, new Date(startDate)));
        }
        if (endDate) {
            feedbackFilters.push(lte(feedback.createdAt, new Date(endDate)));
        }

        const [totalFeedback] = await db
            .select({ count: count() })
            .from(feedback)
            .where(and(
                eq(feedback.widgetId, id),
                ...feedbackFilters
            ));

        // 4. Feedback by Type
        const feedbackByTypeQuery = db
            .select({
                type: feedback.type,
                count: count()
            })
            .from(feedback)
            .where(and(
                eq(feedback.widgetId, id),
                ...feedbackFilters
            ))
            .groupBy(feedback.type);

        const feedbackByType = await feedbackByTypeQuery;

        // 5. Feedback by Status
        const feedbackByStatusQuery = db
            .select({
                status: feedback.status,
                count: count()
            })
            .from(feedback)
            .where(and(
                eq(feedback.widgetId, id),
                ...feedbackFilters
            ))
            .groupBy(feedback.status);

        const feedbackByStatus = await feedbackByStatusQuery;

        // 6. Total Widget Data Entries
        const widgetDataFilters = [];
        if (startDate) {
            widgetDataFilters.push(gte(widgetData.createdAt, new Date(startDate)));
        }
        if (endDate) {
            widgetDataFilters.push(lte(widgetData.createdAt, new Date(endDate)));
        }

        const [totalWidgetData] = await db
            .select({ count: count() })
            .from(widgetData)
            .where(and(
                eq(widgetData.widgetId, id),
                ...widgetDataFilters
            ));

        // 7. Widget Data by Type
        const widgetDataByTypeQuery = db
            .select({
                type: widgetData.type,
                count: count()
            })
            .from(widgetData)
            .where(and(
                eq(widgetData.widgetId, id),
                ...widgetDataFilters
            ))
            .groupBy(widgetData.type);

        const widgetDataByType = await widgetDataByTypeQuery;

        // 8. Daily Analytics Breakdown
        const dailyAnalyticsQuery = db
            .select({
                date: sql<string>`DATE(${analytics.createdAt})`,
                count: count()
            })
            .from(analytics)
            .where(and(
                eq(analytics.widgetId, id),
                ...dateFilters
            ))
            .groupBy(sql`DATE(${analytics.createdAt})`)
            .orderBy(sql`DATE(${analytics.createdAt}) DESC`)
            .limit(30);

        const dailyAnalytics = await dailyAnalyticsQuery;

        // 9. Daily Feedback Breakdown
        const dailyFeedbackQuery = db
            .select({
                date: sql<string>`DATE(${feedback.createdAt})`,
                count: count()
            })
            .from(feedback)
            .where(and(
                eq(feedback.widgetId, id),
                ...feedbackFilters
            ))
            .groupBy(sql`DATE(${feedback.createdAt})`)
            .orderBy(sql`DATE(${feedback.createdAt}) DESC`)
            .limit(30);

        const dailyFeedback = await dailyFeedbackQuery;

        // 10. Unique Sessions Count (from analytics)
        const uniqueSessionsQuery = db
            .selectDistinct({ sessionId: analytics.sessionId })
            .from(analytics)
            .where(and(
                eq(analytics.widgetId, id),
                ...dateFilters
            ));

        const uniqueSessions = await uniqueSessionsQuery;
        const totalUniqueSessions = uniqueSessions.filter(s => s.sessionId).length;

        // 11. Recent Activity (Last 10 events)
        const recentActivityQuery = db
            .select({
                id: analytics.id,
                eventType: analytics.eventType,
                eventData: analytics.eventData,
                sessionId: analytics.sessionId,
                createdAt: analytics.createdAt
            })
            .from(analytics)
            .where(eq(analytics.widgetId, id))
            .orderBy(desc(analytics.createdAt))
            .limit(10);

        const recentActivity = await recentActivityQuery;

        // 12. Response Rate (Feedback with responses vs without)
        const [respondedFeedback] = await db
            .select({ count: count() })
            .from(feedback)
            .where(and(
                eq(feedback.widgetId, id),
                sql`${feedback.response} IS NOT NULL`,
                ...feedbackFilters
            ));

        const responseRate = totalFeedback.count > 0
            ? ((respondedFeedback.count / totalFeedback.count) * 100).toFixed(2)
            : 0;

        return NextResponse.json({
            // Overview Stats
            overview: {
                totalEvents: totalEvents.count || 0,
                totalFeedback: totalFeedback.count || 0,
                totalWidgetData: totalWidgetData.count || 0,
                totalUniqueSessions,
                responseRate: `${responseRate}%`,
                dateRange: {
                    startDate: startDate || "All time",
                    endDate: endDate || "Now"
                }
            },

            // Event Analytics
            analytics: {
                eventsByType: eventsByType.map(item => ({
                    type: item.eventType,
                    count: item.count,
                    percentage: totalEvents.count > 0
                        ? ((item.count / totalEvents.count) * 100).toFixed(1)
                        : 0
                })),
                dailyAnalytics: dailyAnalytics.map(item => ({
                    date: item.date,
                    events: item.count
                }))
            },

            // Feedback Analytics
            feedback: {
                byType: feedbackByType.map(item => ({
                    type: item.type,
                    count: item.count,
                    percentage: totalFeedback.count > 0
                        ? ((item.count / totalFeedback.count) * 100).toFixed(1)
                        : 0
                })),
                byStatus: feedbackByStatus.map(item => ({
                    status: item.status,
                    count: item.count,
                    percentage: totalFeedback.count > 0
                        ? ((item.count / totalFeedback.count) * 100).toFixed(1)
                        : 0
                })),
                dailyFeedback: dailyFeedback.map(item => ({
                    date: item.date,
                    feedback: item.count
                }))
            },

            // Widget Data Analytics
            widgetData: {
                byType: widgetDataByType.map(item => ({
                    type: item.type,
                    count: item.count,
                    percentage: totalWidgetData.count > 0
                        ? ((item.count / totalWidgetData.count) * 100).toFixed(1)
                        : 0
                }))
            },

            // Recent Activity
            recentActivity: recentActivity.map(item => ({
                id: item.id,
                eventType: item.eventType,
                eventData: item.eventData,
                sessionId: item.sessionId,
                timestamp: item.createdAt
            })),

            // Widget Info
            widget: {
                id: foundWidget.id,
                name: foundWidget.name,
                position: foundWidget.position,
                isActive: foundWidget.isActive,
                createdAt: foundWidget.createdAt
            }
        });

    } catch (error) {
        console.error("Analytics error:", error);
        return new NextResponse("Internal Server Error", { status: 500 });
    }
}