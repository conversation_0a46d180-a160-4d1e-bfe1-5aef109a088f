import { create } from "zustand";
import { toast } from "sonner";

interface Feedback {
    id: string;
    widgetId: string;
    type: "positive" | "negative" | "neutral";
    content: string;
    createdAt: Date;
}

interface Message {
    id: string;
    widgetId: string;
    content: string;
    response: string;
    createdAt: Date;
}

interface widgettats {
    widgetId: string;
    name: string;
    totalMessages: number;
    totalFeedback: number;
    positiveFeedback: number;
    negativeFeedback: number;
    averageResponseTime: number;
}

interface DashboardStore {
    // Analytics Data
    totalMessages: number;
    totalFeedback: number;
    positiveFeedbackRate: number;
    averageResponseTime: number;
    recentMessages: Message[];
    recentFeedback: Feedback[];
    widgettats: widgettats[];

    // Loading States
    isLoading: boolean;
    error: string | null;
    hasFetched: boolean;

    // Actions
    fetchDashboardData: () => Promise<void>;
}

export const useDashboardStore = create<DashboardStore>((set, get) => ({
    // Initial State
    totalMessages: 0,
    totalFeedback: 0,
    positiveFeedbackRate: 0,
    averageResponseTime: 0,
    recentMessages: [],
    recentFeedback: [],
    widgettats: [],

    isLoading: false,
    error: null,
    hasFetched: false,

    // Actions
    fetchDashboardData: async () => {
        const { hasFetched, isLoading } = get();

        // Don't fetch if we already have the data or if we're currently loading
        if (hasFetched || isLoading) return;

        set({ isLoading: true, error: null });
        try {
            const response = await fetch("/api/dashboard");
            const data = await response.json();

            if (!response.ok) throw new Error(data.error);

            set({
                totalMessages: data.totalMessages,
                totalFeedback: data.totalFeedback,
                positiveFeedbackRate: data.positiveFeedbackRate,
                averageResponseTime: data.averageResponseTime,
                recentMessages: data.recentMessages.map((msg: any) => ({
                    ...msg,
                    createdAt: new Date(msg.createdAt),
                })),
                recentFeedback: data.recentFeedback.map((feedback: any) => ({
                    ...feedback,
                    createdAt: new Date(feedback.createdAt),
                })),
                widgettats: data.widgettats,
                hasFetched: true,
            });
        } catch (error) {
            set({ error: (error as Error).message });
            toast.error("Failed to fetch dashboard data");
        } finally {
            set({ isLoading: false });
        }
    },
})); 