import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { subscriptionUsage, subscription } from '@/db/schema';
import { eq, and, gte, lte } from 'drizzle-orm';

// GET - Get usage data
export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;
        const { searchParams } = new URL(request.url);

        // Get query parameters
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const action = searchParams.get('action');

        // Get current subscription
        const currentSubscription = await db
            .select()
            .from(subscription)
            .where(eq(subscription.userId, userId))
            .limit(1);

        if (currentSubscription.length === 0) {
            return NextResponse.json({
                usage: [],
                limits: null,
                message: 'No active subscription found',
            });
        }

        // Get current period
        const now = new Date();
        const currentPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

        // Build query conditions
        const conditions = [
            eq(subscriptionUsage.userId, userId),
            eq(subscriptionUsage.period, currentPeriod),
        ];

        // Get usage data
        const usageData = await db
            .select()
            .from(subscriptionUsage)
            .where(and(...conditions));

        // Calculate usage summary
        const usageSummary = usageData.length > 0 ? {
            messageCount: usageData[0].messageCount,
            widgetCount: usageData[0].widgetCount
        } : {
            messageCount: 0,
            widgetCount: 0
        };

        // Get subscription limits (you would typically get this from your plan configuration)
        const limits = {
            messages: 1000,
            widgets: 5,
            // Add more limits as needed
        };

        return NextResponse.json({
            usage: usageSummary,
            limits,
            message: 'Usage data retrieved successfully',
        });

    } catch (error) {
        console.error('Error fetching usage:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// POST - Track usage
export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.id;
        const body = await request.json();
        const { action, count = 1 } = body;

        if (!action) {
            return NextResponse.json(
                { error: 'Action is required' },
                { status: 400 }
            );
        }

        // Get current subscription
        const currentSubscription = await db
            .select()
            .from(subscription)
            .where(eq(subscription.userId, userId))
            .limit(1);

        if (currentSubscription.length === 0) {
            return NextResponse.json(
                { error: 'No active subscription found' },
                { status: 400 }
            );
        }

        // Track usage - update or create usage record for current period
        const now = new Date();
        const currentPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

        // Check if usage record exists for current period
        const existingUsage = await db
            .select()
            .from(subscriptionUsage)
            .where(
                and(
                    eq(subscriptionUsage.userId, userId),
                    eq(subscriptionUsage.period, currentPeriod)
                )
            )
            .limit(1);

        if (existingUsage.length > 0) {
            // Update existing usage
            if (action === 'message') {
                await db
                    .update(subscriptionUsage)
                    .set({
                        messageCount: existingUsage[0].messageCount + count,
                        updatedAt: new Date()
                    })
                    .where(eq(subscriptionUsage.id, existingUsage[0].id));
            } else if (action === 'widget') {
                await db
                    .update(subscriptionUsage)
                    .set({
                        widgetCount: existingUsage[0].widgetCount + count,
                        updatedAt: new Date()
                    })
                    .where(eq(subscriptionUsage.id, existingUsage[0].id));
            }
        } else {
            // Create new usage record
            await db.insert(subscriptionUsage).values({
                userId,
                period: currentPeriod,
                messageCount: action === 'message' ? count : 0,
                widgetCount: action === 'widget' ? count : 0,
            });
        }

        return NextResponse.json({
            message: 'Usage tracked successfully',
        });

    } catch (error) {
        console.error('Error tracking usage:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
} 