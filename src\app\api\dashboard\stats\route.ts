import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { analytics, widget } from '@/db/schema';
import { eq, and, gte, desc, sql, or } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get user's widgets
        const userWidgets = await db.query.widget.findMany({
            where: eq(widget.userId, session.user.id),
        });

        if (userWidgets.length === 0) {
            return NextResponse.json({
                totalResponses: 0,
                activeUsers: 0,
                satisfactionRate: '0%',
                popularTopics: [],
                recentQueries: []
            });
        }

        const widgetIds = userWidgets.map(w => w.id);

        // Get total responses from analytics
        const totalResponses = await db
            .select({ count: sql<number>`count(*)` })
            .from(analytics)
            .where(or(...widgetIds.map(id => eq(analytics.widgetId, id))));

        // Get active users (unique sessions in last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const activeUsers = await db
            .select({ count: sql<number>`count(distinct session_id)` })
            .from(analytics)
            .where(
                and(
                    or(...widgetIds.map(id => eq(analytics.widgetId, id))),
                    gte(analytics.createdAt, thirtyDaysAgo)
                )
            );

        // Get satisfaction rate (based on feedback events)
        const satisfactionData = await db
            .select({
                total: sql<number>`count(*)`,
                satisfied: sql<number>`sum(case when event_data->>'feedback' = 'positive' then 1 else 0 end)`
            })
            .from(analytics)
            .where(
                and(
                    or(...widgetIds.map(id => eq(analytics.widgetId, id))),
                    eq(analytics.eventType, 'feedback_submitted')
                )
            );

        const satisfactionRate = satisfactionData[0]?.total
            ? Math.round((satisfactionData[0].satisfied / satisfactionData[0].total) * 100)
            : 0;

        // Get popular event types
        const popularTopics = await db
            .select({
                topic: analytics.eventType,
                count: sql<number>`count(*)`
            })
            .from(analytics)
            .where(or(...widgetIds.map(id => eq(analytics.widgetId, id))))
            .groupBy(analytics.eventType)
            .orderBy(desc(sql`count(*)`))
            .limit(4);

        // Get recent interactions
        const recentQueries = await db
            .select({
                question: analytics.eventData,
                createdAt: analytics.createdAt,
                eventType: analytics.eventType
            })
            .from(analytics)
            .where(
                and(
                    or(...widgetIds.map(id => eq(analytics.widgetId, id))),
                    eq(analytics.eventType, 'message_sent')
                )
            )
            .orderBy(desc(analytics.createdAt))
            .limit(3);

        return NextResponse.json({
            totalResponses: totalResponses[0]?.count || 0,
            activeUsers: activeUsers[0]?.count || 0,
            satisfactionRate: `${satisfactionRate}%`,
            popularTopics: popularTopics.map(topic => ({
                name: topic.topic,
                count: topic.count
            })),
            recentQueries: recentQueries.map(query => ({
                question: typeof query.question === 'object' ? JSON.stringify(query.question) : query.question,
                time: formatTimeAgo(query.createdAt),
                status: query.eventType
            }))
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        return NextResponse.json(
            { error: 'Failed to fetch dashboard stats' },
            { status: 500 }
        );
    }
}

function formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
        return `${diffInSeconds} seconds ago`;
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
        return `${diffInMinutes} minutes ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
        return `${diffInHours} hours ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
} 