import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { subscription } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Handle subscription webhook events
export async function POST(request: NextRequest) {
    try {
        // Verify webhook signature (implement based on your payment provider)
        // const signature = request.headers.get('x-webhook-signature');
        // if (!verifyWebhookSignature(request.body, signature)) {
        //   return NextResponse.json(
        //     { error: 'Invalid webhook signature' },
        //     { status: 401 }
        //   );
        // }

        const body = await request.json();
        const { event, data } = body;

        // Handle different webhook events
        switch (event) {
            case 'subscription.created':
            case 'subscription.updated':
                await handleSubscriptionUpdate(data);
                break;

            case 'subscription.cancelled':
                await handleSubscriptionCancellation(data);
                break;

            case 'subscription.payment_failed':
                await handlePaymentFailure(data);
                break;

            case 'subscription.payment_succeeded':
                await handlePaymentSuccess(data);
                break;

            default:
                console.warn(`Unhandled webhook event: ${event}`);
                return NextResponse.json(
                    { message: 'Event not handled' },
                    { status: 200 }
                );
        }

        return NextResponse.json({
            message: 'Webhook processed successfully',
        });

    } catch (error) {
        console.error('Error processing webhook:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Helper functions for handling different webhook events
async function handleSubscriptionUpdate(data: any) {
    const { userId, planId, status, startDate, endDate } = data;

    await db
        .update(subscription)
        .set({
            plan: planId,
            status,
            currentPeriodStart: new Date(startDate),
            currentPeriodEnd: new Date(endDate),
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));
}

async function handleSubscriptionCancellation(data: any) {
    const { userId } = data;

    await db
        .update(subscription)
        .set({
            status: 'cancelled',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));
}

async function handlePaymentFailure(data: any) {
    const { userId } = data;

    await db
        .update(subscription)
        .set({
            status: 'payment_failed',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));
}

async function handlePaymentSuccess(data: any) {
    const { userId } = data;

    await db
        .update(subscription)
        .set({
            status: 'active',
            updatedAt: new Date(),
        })
        .where(eq(subscription.userId, userId));
} 