import { db } from '@/lib/db';
import { user } from '@/db/schema';
import { eq } from 'drizzle-orm';

export interface UserData {
    id: string;
    email: string;
    name: string;
    image?: string | null;
}

/**
 * Ensures a user exists in the database, creating them if they don't exist
 */
export async function ensureUserExists(userData: UserData): Promise<boolean> {
    try {
        // Check if user exists
        const existingUser = await db
            .select({ id: user.id })
            .from(user)
            .where(eq(user.id, userData.id))
            .limit(1);

        if (existingUser.length === 0) {
            // User doesn't exist, create them
            await db.insert(user).values({
                id: userData.id,
                email: userData.email,
                name: userData.name,
                image: userData.image || null,
            });
            console.log(`Created user record for ${userData.email}`);
        }

        return true;
    } catch (error) {
        console.error('Error ensuring user exists:', error);
        return false;
    }
}

/**
 * Checks if a user exists in the database
 */
export async function userExists(userId: string): Promise<boolean> {
    try {
        const existingUser = await db
            .select({ id: user.id })
            .from(user)
            .where(eq(user.id, userId))
            .limit(1);

        return existingUser.length > 0;
    } catch (error) {
        console.error('Error checking if user exists:', error);
        return false;
    }
}
